用户token：
j6JsdKy_EGKnk4P-tBDE87Y6u17of_WAJSgyYurTKes



类型: 通用数英1~4位   

说明：

1.本接口是通用数英识别接口，只能识别相对清楚简单的数英字符图；

2.尽量多换几张不同的图来识别，才能评估接口对识别图的整体识别效果；

3.对于混淆程度高，识别难度大，本接口识别效果较差的图，后续可换到10103，50103等接口去改善识别。

只支持HTTP中的Post方法，同时Content-Type请设置为application/json或者application/x-www-form-urlencoded

跨平台http接口,请仔细查看各类型的http接口,仅支持post传参

http api 接口 文档

1.1.请求地址

http://api.jfbym.com/api/YmServer/customApi

1.2.参数列表

参数名称	传值类型	说明	是否必须
image	string	待识别图的base64字符串	是
token	string	用户中心Token	是
type	string	10110	是
2.1.反参
参数名称	传值类型	说明
code	int	状态值
msg	string	请求说明
data	array	打码数据
--code	int	打码服务状态
--data	string	识别结果
--time	string	打码服务时长
2.2.code列表
code	说明
10000	识别成功
10001	参数错误
10002	余额不足
10003	无此访问权限（未获取到有效token）
10004	无此验证类型（type参数值不对）
10005	网络拥塞
10006	数据包过载
10007	服务繁忙（接口模型无法处理图片参数）
10008	网络错误，请稍后重试
10009	结果准备中，请稍后再试
10010	请求结束

示例：
示例
复制
import base64
import requests
# www.jfbym.com  注册后登录去用户中心
with open('图片绝对路径/test.png', 'rb') as f:
    b = base64.b64encode(f.read()).decode()  ## 图片二进制流base64字符串
def verify():
    url = "http://api.jfbym.com/api/YmServer/customApi"
    data = {
        ## 关于参数,一般来说有3个;不同类型id可能有不同的参数个数和参数名,找客服获取
        "token": "注册后登录去用户中心获取token",
        "type": "打码类型id",
        "image": b,
    }
    _headers = {
        "Content-Type": "application/json"
    }
    response = requests.request("POST", url, headers=_headers, json=data).json()
    print(response)
if __name__ == '__main__':
    verify()